const { expect, assert } = require('chai');
const request = require('supertest');
const sinon = require('sinon');
const { app, validImagePath } = require('./common');
const User = require('../models/user');
const BanAppeal = require('../models/ban-appeal');
const ProfileTempBanAppeal = require('../models/profile-temp-ban-appeal');
const reportLib = require('../lib/report');
const { setMockPromptResponse } = require('./stub');

describe('ban notice', async () => {
  beforeEach(async () => {
    // create admin user
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
    expect(res.status).to.equal(200);

    user = await User.findById('1');
    user.admin = true;
    user.adminPermissions = { all: true };
    await user.save();
  });

  it('eu user sees ban notice', async function () {

    // Panama
    ip = '**********';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 1)
      .send({ user: '0', bannedReason: 'Scammer' });
    expect(res.status).to.equal(200);

    // not EU, so no ban notice
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    // change to EU
    // Lithuania
    ip = '***************';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'scamming',
      appealStatus: 'allowed',
    });

    // changing ip does not clear the ban notice
    // Panama
    ip = '**********';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'scamming',
      appealStatus: 'allowed',
    });

    // unbanning should clear the ban notice
    res = await request(app)
      .put('/v1/admin/unban')
      .set('authorization', 1)
      .send({ user: '0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();
  });

  it('unmapped ban reasons should trigger ban notice with default ban reason spam', async function () {

    // Lithuania
    ip = '***************';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 1)
      .send({ user: '0', bannedReason: 'Other' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'spam',
      appealStatus: 'allowed',
    });
  });

  it('undefined ban reason should trigger ban notice with default ban reason spam', async function () {

    // Lithuania
    ip = '***************';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    user = await User.findById('0');
    user.shadowBanned = true;
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'spam',
      appealStatus: 'allowed',
    });
  });

  it('country ban reasons should trigger ban notice with default ban reason spam', async function () {

    // Lithuania
    ip = '***************';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 1)
      .send({ user: '0', bannedReason: 'Auto-ban: Nigeria' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'spam',
      appealStatus: 'allowed',
    });
  });

  it('multiple ban reasons', async function () {

    // Lithuania
    ip = '***************';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    // ban user multiple banned reasons
    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 1)
      .send({
        user: '0',
        bannedReasons: ['scamming', 'creating multiple accounts'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'scamming, creating multiple accounts',
      reasons: [ 'scamming', 'creating multiple accounts' ],
      appealStatus: 'allowed',
    });
  });

  it('translated ban reason - single', async function () {

    // Lithuania
    ip = '***************';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({ locale: 'ja' })
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    user = await User.findById('0');
    user.shadowBanned = true;
    user.bannedReason = 'Other';
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'スパム',
      appealStatus: 'allowed',
    });

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({ locale: 'ko' })
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: '스팸',
      appealStatus: 'allowed',
    });
  });

  it('translated ban reason - multiple', async function () {

    // Lithuania
    ip = '***************';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({ locale: 'ja' })
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    user = await User.findById('0');
    user.shadowBanned = true;
    user.bannedReason = 'Other';
    user.bannedReasons = ['scamming', 'creating multiple accounts'];
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: '詐欺行為、複数のアカウントを作成したこと',
      reasons: [ '詐欺行為', '複数のアカウントを作成したこと' ],
      appealStatus: 'allowed',
    });

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({ locale: 'ko' })
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: '사기 행위 및 여러 개의 계정을 만드는 행위',
      reasons: [ '사기 행위', '여러 개의 계정을 만드는 행위' ],
      appealStatus: 'allowed',
    });
  });

  it('give ban notice when free banned user tries to purchase', async function () {

    // Panama
    ip = '**********';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    res = await request(app)
      .get('/v1/user/isPurchaseAllowed')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.isPurchaseAllowed).to.equal(true);
    expect(res.body.banNotice).to.equal();

    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 1)
      .send({ user: '0', bannedReason: 'Scammer' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    res = await request(app)
      .get('/v1/user/isPurchaseAllowed')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.isPurchaseAllowed).to.equal(false);
    expect(res.body.banNotice).to.eql({
      reason: 'scamming',
      appealStatus: 'allowed',
    });

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'scamming',
      appealStatus: 'allowed',
    });
  });

  it('unverified ban reasons should not trigger ban notice', async function () {

    // Lithuania
    ip = '***************';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 1)
      .send({ user: '0', bannedReason: 'unverified with infringing text' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();
  });

  it('temp shadow ban should not trigger ban notice', async function () {

    // Lithuania
    ip = '***************';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    // trigger temp shadow ban
    setMockPromptResponse('{"ban": true, "reason": "Nudity/Sexual Content"}');
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    let user = await User.findOne({ _id: '0' });
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedBy).to.equal('openai');
    expect(user.bannedReason).to.equal('temp shadow ban due to inappropriate profile');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.profileTempBanReason).to.equal('Nudity/Sexual Content');
    expect(res.body.user.banNotice).to.equal();
  });

  it('user with revenue sees ban notice even outside GDPR countries', async function () {

    // Panama (non-GDPR country)
    ip = '**********';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    user = await User.findById('0');
    user.metrics.revenue = 9.99; // User has made a purchase
    await user.save();

    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 1)
      .send({ user: '0', bannedReason: 'Scammer' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql();

    // User with revenue should see ban notice even in non-GDPR country in app version 1.13.88
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({ appVersion: '1.13.88' });
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'scamming',
      appealStatus: 'allowed',
    });

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({ appVersion: '1.13.89' });
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'scamming',
      appealStatus: 'allowed',
    });
  });

  it('user with no revenue in non-GDPR country does not see ban notice', async function () {

    // Panama (non-GDPR country)
    ip = '**********';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({ appVersion: '1.13.89' })
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 1)
      .send({ user: '0', bannedReason: 'Scammer' });
    expect(res.status).to.equal(200);

    // User with no revenue in non-GDPR country should not see ban notice
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({ appVersion: '1.13.89' });
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();
  });
});


describe('appeals', async () => {
  let appealId;
  beforeEach(async () => {
    // create admin user
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
    expect(res.status).to.equal(200);

    user = await User.findById('1');
    user.admin = true;
    user.adminPermissions = { all: true };
    await user.save();

    // create eu user with ban notice
    // Lithuania
    ip = '***************';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 1)
      .send({ user: '0', bannedReason: 'Scammer' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'scamming',
      appealStatus: 'allowed',
    });

    // submit appeal
    res = await request(app)
      .put('/v1/user/banAppeal')
      .set('authorization', 0)
      .send({ comment: 'not scammer' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'scamming',
      appealStatus: 'pending',
    });

    // get appeals
    res = await request(app)
      .get('/v1/admin/banAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(1);
    console.log(res.body.appeals[0]);
    expect(res.body.appeals[0].user).to.equal('0');
    expect(res.body.appeals[0].comment).to.equal('not scammer');
    appealId = res.body.appeals[0]._id;

    appeal = await BanAppeal.findById(appealId);
    expect(appeal.bannedReasons).to.eql(['Scammer']);
    expect(appeal.banNoticeReason).to.equal('scamming');
  });

  it('reject', async function () {
    res = await request(app)
      .put('/v1/admin/banAppeal/decision')
      .set('authorization', 1)
      .send({ appealId, decision: 'rejected', notes: 'is scammer' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/banAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'scamming',
      appealStatus: 'rejected',
    });

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('Scammer');

    appeal = await BanAppeal.findById(appealId);
    expect(appeal.reviewedBy).to.equal('1');
    expect(appeal.decision).to.equal('rejected');
    expect(appeal.notes).to.equal('is scammer');

    // cannot appeal again
    res = await request(app)
      .put('/v1/user/banAppeal')
      .set('authorization', 0)
      .send({ comment: 'definitely not scammer' })
    expect(res.status).to.equal(403);

    res = await request(app)
      .get('/v1/admin/banAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    user = await User.findById('0');
    console.log(user.banHistory);
    expect(user.banHistory.length).to.equal(3);
    expect(user.banHistory[0].action).to.equal('ban');
    expect(user.banHistory[0].by).to.equal('1');
    expect(user.banHistory[0].reason).to.equal('Scammer');
    expect(user.banHistory[0].notes).to.equal();
    expect(user.banHistory[1].action).to.equal('appealSubmitted');
    expect(user.banHistory[1].by).to.equal('0');
    expect(user.banHistory[1].reason).to.equal();
    expect(user.banHistory[1].notes).to.equal('not scammer');
    expect(user.banHistory[2].action).to.equal('appealRejected');
    expect(user.banHistory[2].by).to.equal('1');
    expect(user.banHistory[2].reason).to.equal();
    expect(user.banHistory[2].notes).to.equal('is scammer');
  });

  it('approve', async function () {
    res = await request(app)
      .put('/v1/admin/banAppeal/decision')
      .set('authorization', 1)
      .send({ appealId, decision: 'approved', notes: 'is not scammer' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/banAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.bannedReason).to.equal();

    appeal = await BanAppeal.findById(appealId);
    expect(appeal.reviewedBy).to.equal('1');
    expect(appeal.decision).to.equal('approved');
    expect(appeal.notes).to.equal('is not scammer');

    // cannot appeal again
    res = await request(app)
      .put('/v1/user/banAppeal')
      .set('authorization', 0)
      .send({ comment: 'definitely not scammer' })
    expect(res.status).to.equal(403);

    res = await request(app)
      .get('/v1/admin/banAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    user = await User.findById('0');
    console.log(user.banHistory);
    expect(user.banHistory.length).to.equal(4);
    expect(user.banHistory[0].action).to.equal('ban');
    expect(user.banHistory[0].by).to.equal('1');
    expect(user.banHistory[0].reason).to.equal('Scammer');
    expect(user.banHistory[0].notes).to.equal();
    expect(user.banHistory[1].action).to.equal('appealSubmitted');
    expect(user.banHistory[1].by).to.equal('0');
    expect(user.banHistory[1].reason).to.equal();
    expect(user.banHistory[1].notes).to.equal('not scammer');
    expect(user.banHistory[2].action).to.equal('appealApproved');
    expect(user.banHistory[2].by).to.equal('1');
    expect(user.banHistory[2].reason).to.equal();
    expect(user.banHistory[2].notes).to.equal('is not scammer');
    expect(user.banHistory[3].action).to.equal('unban');
    expect(user.banHistory[3].by).to.equal('1');
    expect(user.banHistory[3].reason).to.equal();
    expect(user.banHistory[3].notes).to.equal('is not scammer (appeal approved)');
  });

  it('one appeal allowed per ban', async function () {
    res = await request(app)
      .put('/v1/admin/banAppeal/decision')
      .set('authorization', 1)
      .send({ appealId, decision: 'approved', notes: 'is not scammer' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/banAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.bannedReason).to.equal();

    appeal = await BanAppeal.findById(appealId);
    expect(appeal.reviewedBy).to.equal('1');
    expect(appeal.decision).to.equal('approved');
    expect(appeal.notes).to.equal('is not scammer');

    // ban again
    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 1)
      .send({ user: '0', bannedReason: 'Spam' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'spam',
      appealStatus: 'allowed',
    });

    // can appeal again
    res = await request(app)
      .put('/v1/user/banAppeal')
      .set('authorization', 0)
      .send({ comment: 'not spam' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/banAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(1);
    console.log(res.body.appeals[0]);
    expect(res.body.appeals[0].user).to.equal('0');
    expect(res.body.appeals[0].comment).to.equal('not spam');
    appealId = res.body.appeals[0]._id;
  });

  it('test mass unban when ban appeal approved', async () => {
    let res = await request(app)
      .put('/v1/admin/banAppeal/decision')
      .set('authorization', 1)
      .send({ appealId, decision: 'approved', notes: 'is not scammer' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/banAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    user = await User.findById('0');
    user.deviceId = 'device1';
    await user.save();
    expect(user.shadowBanned).to.equal(false);
    expect(user.bannedReason).to.equal();

    appeal = await BanAppeal.findById(appealId);
    expect(appeal.reviewedBy).to.equal('1');
    expect(appeal.decision).to.equal('approved');
    expect(appeal.notes).to.equal('is not scammer');

    for (let i = 2; i <= 10; i++) {
      // create more users on the same device
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ deviceId: 'device1' });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/admin/ban')
        .set('authorization', 1)
        .send({ user: `${i}`, bannedReason: 'Spam' });
      expect(res.status).to.equal(200);
    }

    // ban again
    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 1)
      .send({ user: '0', bannedReason: 'Spam' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'spam',
      appealStatus: 'allowed',
    });

    // can appeal again
    res = await request(app)
      .put('/v1/user/banAppeal')
      .set('authorization', 0)
      .send({ comment: 'not spam' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/banAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(1);
    console.log(res.body.appeals[0]);
    expect(res.body.appeals[0].user).to.equal('0');
    expect(res.body.appeals[0].comment).to.equal('not spam');
    appealId = res.body.appeals[0]._id;

    res = await request(app)
      .put('/v1/admin/banAppeal/decision')
      .set('authorization', 1)
      .send({ appealId, decision: 'approved', notes: 'is not spam' });
    expect(res.status).to.equal(200);

    await new Promise(resolve => setTimeout(resolve, 200));

    for (let i = 2; i <= 10; i++) {
      let user = await User.findById(`${i}`);
      expect(user.shadowBanned).to.equal(false);
      expect(user.bannedReason).to.equal();
      expect(user.bannedNotes).to.equal();
      expect(user.banHistory[user.banHistory.length - 1].notes).to.equal(`is not spam (appeal approved) (mass unban with device ID: device1)`);
    }
  });
});

describe('underage birthday ban handling', async () => {

  it('should automatically ban user under 18 and unban user when they turn 18', async function () {
    // Lithuania
    ip = '***************';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);

    const seventeenYearsAgo = new Date();
    seventeenYearsAgo.setFullYear(seventeenYearsAgo.getFullYear() - 17);

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', 0)
      .send({
        year: seventeenYearsAgo.getFullYear(),
        month: seventeenYearsAgo.getMonth() + 1,
        day: seventeenYearsAgo.getDate(),
      });
    expect(res.status).to.equal(200);
    expect(res.body.birthday).to.exist;
    expect(res.body.banNotice).to.eql({
      reason: 'underage',
      appealStatus: 'allowed',
    });

    // Verify they are banned
    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('underage birthday');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'underage',
      appealStatus: 'allowed',
    });

    // Fast forward time to next month
    let clock = sinon.useFakeTimers({
      now: Date.now() + 30 * 24 * 60 * 60 * 1000,
    });

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'underage',
      appealStatus: 'allowed',
    });

    // Fast forward time to next year
    clock = sinon.useFakeTimers({
      now: Date.now() + 365 * 24 * 60 * 60 * 1000,
    });

    // Call initApp which should trigger the unban logic
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    // Verify user is no longer banned
    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.bannedReason).to.equal();
    expect(user.banNotice).to.equal();

    clock.restore();
  });

  it('should not unban users banned for other reasons when they turn 18', async function () {
    // Lithuania (EU country to see ban notice)
    ip = '***************';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
    expect(res.status).to.equal(200);

    user = await User.findById('1');
    user.admin = true;
    user.adminPermissions = { all: true };
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 1)
      .send({ user: '0', bannedReason: 'Scammer' });
    expect(res.status).to.equal(200);

    const eighteenYearsAgo = new Date();
    eighteenYearsAgo.setFullYear(eighteenYearsAgo.getFullYear() - 18);

    user = await User.findById('0');
    user.birthday = eighteenYearsAgo;
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'scamming',
      appealStatus: 'allowed',
    });

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('Scammer');
  });

  it('should handle age calculation with user timezone correctly', async function () {

    let clock = sinon.useFakeTimers({ now: new Date("2025-07-17T01:00:00Z").getTime() });

    // Initialize user
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ timezone: 'America/Los_Angeles' })
    expect(res.status).to.equal(200);

    let user = await User.findById('0');
    expect(user.timezone).to.equal('America/Los_Angeles');

    const birthday = new Date('2007-07-17')

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', 0)
      .send({
        year: birthday.getFullYear(),
        month: birthday.getMonth() + 1,
        day: birthday.getDate(),
      });
    expect(res.status).to.equal(200);
    expect(res.body.birthday).to.exist;
    expect(res.body.banNotice).to.eql({
      reason: 'underage',
      appealStatus: 'allowed',
    });

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('underage birthday');
    expect(user.age).to.equal(17);

    // now time zone in Asia/Singapore will be ahead than UTC time
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ timezone: 'Asia/Singapore' })
    expect(res.status).to.equal(200);

    user = await User.findById('1');
    expect(user.timezone).to.equal('Asia/Singapore');

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', 1)
      .send({
        year: birthday.getFullYear(),
        month: birthday.getMonth() + 1,
        day: birthday.getDate(),
      });
    expect(res.status).to.equal(200);
    expect(res.body.birthday).to.exist;

    user = await User.findById('1');
    expect(user.shadowBanned).to.equal(false);
    expect(user.age).to.equal(18);

    clock.restore();
  });
});

describe('profile temp ban appeals', async () => {
  let appealId, pictureKey, pictureKey2;
  let user, res, ip, appeal;
  beforeEach(async () => {
    // create admin user
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
    expect(res.status).to.equal(200);

    user = await User.findById('1');
    user.admin = true;
    user.adminPermissions = { all: true };
    await user.save();

    // create eu user with profile temp ban
    // Lithuania
    ip = '***************';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);

    // upload picture
    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    // upload picture
    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    user = await User.findById('0').lean()
    pictureKey = user.pictures[0];
    pictureKey2 = user.pictures[1];

    // First report to start the process
    setMockPromptResponse('{"ban": false}');
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    // trigger profile temp ban
    setMockPromptResponse(`{"ban": true, "reason": "Nudity/Sexual Content", "violationLocation": "profile", "infringingText": ["nude text"], "infringingPictures": [0]}`);
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.profileTempBanReason).to.equal('Nudity/Sexual Content');
    expect(res.body.user.banNotice).to.eql();
    expect(res.body.user.accountRestrictions).to.eql({
      profileTempBan: {
        appealStatus: 'allowed',
      },
    });

    user = await User.findById('0').lean()
    expect(user.shadowBanned).to.equal(true);
    expect(user.profileTempBanReason).to.equal('Nudity/Sexual Content');
    expect(user.profileTempBanInfringingText).to.eql(['nude text']);
    expect(user.profileTempBanInfringingPictures).to.eql([pictureKey]);
    expect(user.accountRestrictions).to.eql({
      profileTempBan: {
        appealStatus: 'allowed',
      },
    });

    // submit temp ban appeal
    res = await request(app)
      .put('/v1/user/tempBanAppeal')
      .set('authorization', 0)
      .send({ comment: 'not inappropriate' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql();
    expect(res.body.user.accountRestrictions).to.eql({
      profileTempBan: {
        appealStatus: 'pending',
      },
    });

    // get temp ban appeals
    res = await request(app)
      .get('/v1/admin/tempBanAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(1);
    expect(res.body.appeals[0].user).to.equal('0');
    expect(res.body.appeals[0].comment).to.equal('not inappropriate');
    appealId = res.body.appeals[0]._id;

    appeal = await ProfileTempBanAppeal.findById(appealId);
    expect(appeal.profileTempBanReason).to.equal('Nudity/Sexual Content');
    expect(appeal.profileTempBanInfringingText).to.eql(['nude text']);
    expect(appeal.profileTempBanInfringingPictures).to.eql([pictureKey]);

    user = await User.findById('0').lean()
    expect(user.banHistory.length).to.equal(2);
    expect(user.banHistory[0].action).to.equal('ban');
    expect(user.banHistory[1].action).to.equal('profileTempBanAppealSubmitted');

    expect(user.shadowBanned).to.equal(true);
    expect(user.profileTempBanReason).to.equal('Nudity/Sexual Content');
    expect(user.accountRestrictions).to.eql({
      profileTempBan: {
        appealStatus: 'pending',
      },
    });
    expect(user.profileTempBanInfringingText).to.eql(['nude text']);
    expect(user.profileTempBanInfringingPictures).to.eql([pictureKey]);
  });

  it('reject temp ban appeal', async function () {

    let tempBanAppealData = await ProfileTempBanAppeal.find({ user: '0' });
    expect(tempBanAppealData.length).to.equal(1);
    expect(tempBanAppealData[0].decision).to.equal();

    let user = await User.findById('0').lean()
    expect(user.shadowBanned).to.equal(true);
    expect(user.profileTempBanReason).to.equal('Nudity/Sexual Content');
    expect(user.accountRestrictions).to.eql({
      profileTempBan: {
        appealStatus: 'pending',
      },
    });

    res = await request(app)
      .put('/v1/admin/tempBanAppeal/decision')
      .set('authorization', 1)
      .send({ appealId, decision: 'rejected', notes: 'is truly inappropriate' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/tempBanAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    tempBanAppealData = await ProfileTempBanAppeal.find({ user: '0' });
    expect(tempBanAppealData.length).to.equal(1);
    expect(tempBanAppealData[0].decision).to.equal('rejected');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql();
    expect(res.body.user.accountRestrictions).to.eql({
      profileTempBan: {
        appealStatus: 'rejected',
      },
    });

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.profileTempBanReason).to.equal('Nudity/Sexual Content');

    appeal = await ProfileTempBanAppeal.findById(appealId);
    expect(appeal.reviewedBy).to.equal('1');
    expect(appeal.decision).to.equal('rejected');
    expect(appeal.notes).to.equal('is truly inappropriate');

    // cannot appeal again
    res = await request(app)
      .put('/v1/user/tempBanAppeal')
      .set('authorization', 0)
      .send({ comment: 'definitely not inappropriate' })
    expect(res.status).to.equal(403);

    res = await request(app)
      .get('/v1/admin/tempBanAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    user = await User.findById('0');
    expect(user.banHistory.length).to.equal(3);
    expect(user.banHistory[0].action).to.equal('ban');
    expect(user.banHistory[0].by).to.equal('openai');
    expect(user.banHistory[1].action).to.equal('profileTempBanAppealSubmitted');
    expect(user.banHistory[1].by).to.equal('0');
    expect(user.banHistory[1].notes).to.equal('not inappropriate');
    expect(user.banHistory[2].action).to.equal('profileTempBanAppealRejected');
    expect(user.banHistory[2].by).to.equal('1');
    expect(user.banHistory[2].notes).to.equal('is truly inappropriate');
  });

  it('approve temp ban appeal', async function () {
    user = await User.findById('0');
    const originalInfringingText = user.profileTempBanInfringingText;
    const originalInfringingPictures = user.profileTempBanInfringingPictures;

    res = await request(app)
      .put('/v1/admin/tempBanAppeal/decision')
      .set('authorization', 1)
      .send({ appealId, decision: 'approved', notes: 'is not inappropriate' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/tempBanAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();
    expect(res.body.user.accountRestrictions).to.eql({});

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.profileTempBanReason).to.equal();
    expect(user.appealedProfileTempBanInfringingText).to.eql(originalInfringingText);
    expect(user.appealedProfileTempBanInfringingPictures).to.eql(originalInfringingPictures);

    appeal = await ProfileTempBanAppeal.findById(appealId);
    expect(appeal.reviewedBy).to.equal('1');
    expect(appeal.decision).to.equal('approved');
    expect(appeal.notes).to.equal('is not inappropriate');

    // cannot appeal again
    res = await request(app)
      .put('/v1/user/tempBanAppeal')
      .set('authorization', 0)
      .send({ comment: 'definitely not inappropriate' })
    expect(res.status).to.equal(403);

    res = await request(app)
      .get('/v1/admin/tempBanAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    user = await User.findById('0');
    expect(user.banHistory.length).to.equal(4);
    expect(user.banHistory[0].action).to.equal('ban');
    expect(user.banHistory[0].by).to.equal('openai');
    expect(user.banHistory[1].action).to.equal('profileTempBanAppealSubmitted');
    expect(user.banHistory[1].by).to.equal('0');
    expect(user.banHistory[1].notes).to.equal('not inappropriate');
    expect(user.banHistory[2].action).to.equal('profileTempBanAppealApproved');
    expect(user.banHistory[2].by).to.equal('1');
    expect(user.banHistory[2].notes).to.equal('is not inappropriate');
    expect(user.banHistory[3].action).to.equal('unban');
    expect(user.banHistory[3].notes).to.equal('undo temp shadow ban due to inappropriate profile');
  });

  it('prevent re-banning for same appealed content', async function () {
    res = await request(app)
      .put('/v1/admin/tempBanAppeal/decision')
      .set('authorization', 1)
      .send({ appealId, decision: 'approved', notes: 'is not inappropriate' })
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.appealedProfileTempBanInfringingText).to.eql(['nude text']);

    // Try to trigger profile temp ban again with same infringing text
    setMockPromptResponse('{"ban": true, "reason": "Nudity/Sexual Content", "violationLocation": "profile", "infringingText": ["nude text"]}');
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.profileTempBanReason).to.equal();

    setMockPromptResponse('{"ban": true, "reason": "Nudity/Sexual Content", "violationLocation": "profile", "infringingText": ["nude"]}');
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.profileTempBanReason).to.equal();

    setMockPromptResponse('{"ban": true, "reason": "Nudity/Sexual Content", "violationLocation": "profile", "infringingText": ["nude text content"]}');
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.profileTempBanReason).to.equal();

    setMockPromptResponse('{"ban": true, "reason": "Nudity/Sexual Content", "violationLocation": "profile", "infringingText": ["different inappropriate text"]}');
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.profileTempBanReason).to.equal('Nudity/Sexual Content');
    expect(user.profileTempBanInfringingText).to.eql(['different inappropriate text']);
  });

  it('prevent re-banning for same appealed pictures', async function () {
    res = await request(app)
      .put('/v1/admin/tempBanAppeal/decision')
      .set('authorization', 1)
      .send({ appealId, decision: 'approved', notes: 'is not inappropriate' })
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);

    setMockPromptResponse(`{"ban": true, "reason": "Nudity/Sexual Content", "violationLocation": "profile", "infringingPictures": [0]}`);
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.profileTempBanReason).to.equal();

    setMockPromptResponse(`{"ban": true, "reason": "Nudity/Sexual Content", "violationLocation": "profile", "infringingPictures": [1]}`);
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.profileTempBanReason).to.equal('Nudity/Sexual Content');
    expect(user.profileTempBanInfringingPictures).to.eql([pictureKey2]);
    expect(user.appealedProfileTempBanInfringingText).to.eql(['nude text']);
    expect(user.appealedProfileTempBanInfringingPictures).to.eql([pictureKey]);
  });

  it('mixed content filtering - partial match', async function () {
    res = await request(app)
      .put('/v1/admin/tempBanAppeal/decision')
      .set('authorization', 1)
      .send({ appealId, decision: 'approved', notes: 'is not inappropriate' })
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);

    // Try to trigger profile temp ban with mix of appealed and new content
    setMockPromptResponse('{"ban": true, "reason": "Nudity/Sexual Content", "violationLocation": "profile", "infringingText": ["nude text", "new bad text"], "infringingPictures": [1]}');
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.profileTempBanReason).to.equal('Nudity/Sexual Content');
    expect(user.profileTempBanInfringingText).to.eql(['new bad text']);
    expect(user.profileTempBanInfringingPictures).to.eql([pictureKey2]);

    //init app
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/tempBanAppeal')
      .set('authorization', 0)
      .send({ comment: 'not inappropriate ban' })
    expect(res.status).to.equal(200);

    // Get the new appeal ID
    res = await request(app)
      .get('/v1/admin/tempBanAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(1);
    const newAppealId = res.body.appeals[0]._id;

    res = await request(app)
      .put('/v1/admin/tempBanAppeal/decision')
      .set('authorization', 1)
      .send({ appealId: newAppealId, decision: 'approved', notes: 'is not inappropriate second time' })
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.appealedProfileTempBanInfringingText).to.eql(['nude text', 'new bad text']);
    expect(user.appealedProfileTempBanInfringingPictures).to.eql([pictureKey, pictureKey2]);
  });

  it('appeal status reset for new ban after rejected appeal', async function () {
    res = await request(app)
      .put('/v1/admin/tempBanAppeal/decision')
      .set('authorization', 1)
      .send({ appealId, decision: 'rejected', notes: 'is truly inappropriate' })
    expect(res.status).to.equal(200);

    let user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.profileTempBanReason).to.equal('Nudity/Sexual Content');
    expect(user.accountRestrictions.profileTempBan.appealStatus).to.equal('rejected');

    await reportLib.undoProfileTempBan(user);
    await user.save();

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.profileTempBanReason).to.equal();
    expect(user.accountRestrictions.profileTempBan.appealStatus).to.equal('rejected');

    setMockPromptResponse('{"ban": true, "reason": "Harassment", "violationLocation": "profile", "infringingText": ["harassment text"]}');
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Profile'],
        comment: 'New inappropriate content',
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.profileTempBanReason).to.equal('Harassment');
    expect(user.profileTempBanInfringingText).to.eql(['harassment text']);
    expect(user.accountRestrictions.profileTempBan.appealStatus).to.equal('allowed');

    // Verify user can now appeal the new ban
    res = await request(app)
      .put('/v1/user/tempBanAppeal')
      .set('authorization', 0)
      .send({ comment: 'this new ban is not appropriate' })
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.accountRestrictions.profileTempBan.appealStatus).to.equal('pending');

    const appeals = await ProfileTempBanAppeal.find({ user: '0' }).sort('createdAt');
    expect(appeals.length).to.equal(2); // Original appeal + new appeal
    expect(appeals[1].profileTempBanReason).to.equal('Harassment');
    expect(appeals[1].profileTempBanInfringingText).to.eql(['harassment text']);
    expect(appeals[1].comment).to.equal('this new ban is not appropriate');
  });

  it('account restrictions initialization for GDPR users', async function () {
    // Test user without accountRestrictions in GDPR country
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .set('X-Forwarded-For', '***************') // Lithuania
    expect(res.status).to.equal(200);
    expect(res.body.user.accountRestrictions).to.eql({});

    // Trigger profile temp ban
    setMockPromptResponse('{"ban": true, "reason": "Nudity/Sexual Content", "violationLocation": "profile"}');
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '2',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .set('X-Forwarded-For', '***************') // Lithuania
    expect(res.status).to.equal(200);

    expect(res.body.user.accountRestrictions).to.eql({
      profileTempBan: {
        appealStatus: 'allowed',
      },
    });
  });

  it('account restrictions initialization for non GDPR users', async function () {
    // Test user without accountRestrictions in non GDPR country
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .set('X-Forwarded-For', '**********') // Panama
    expect(res.status).to.equal(200);
    expect(res.body.user.accountRestrictions).to.eql(null);

    setMockPromptResponse('{"ban": true, "reason": "Nudity/Sexual Content", "violationLocation": "profile"}');
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '2',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .set('X-Forwarded-For', '**********') // Panama
    expect(res.status).to.equal(200);
    expect(res.body.user.accountRestrictions).to.eql(null);

    res = await request(app)
      .put('/v1/user/tempBanAppeal')
      .set('authorization', 2)
      .send({ comment: 'not inappropriate' })
    expect(res.status).to.equal(403);

    user = await User.findById('2');
    user.metrics.revenue = 1
    await user.save()

    res = await request(app)
      .put('/v1/user/tempBanAppeal')
      .set('authorization', 2)
      .send({ comment: 'not inappropriate' })
    expect(res.status).to.equal(403);

  });
});

