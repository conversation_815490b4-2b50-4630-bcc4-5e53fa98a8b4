const Profile = require('../models/profile');
const Subcategory = require('../models/subcategory');
const Category = require('../models/category');
const SitemapPaginatedSubcategories = require('../models/sitemap-paginated-subcategories');
const SitemapPaginatedCategories = require('../models/sitemap-paginated-categories');
const SitemapPaginatedProfiles = require('../models/sitemap-paginated-profiles');
const { getSitemapPaginationPageSize } = require('./constants');
const BATCH_SIZE = 1000;

async function createPaginatedProfilesForSubcategory(subcategoryId) {
  
  const pageSize = getSitemapPaginationPageSize();
  const profiles = await Profile
    .find({ subcategories: subcategoryId })
    .select({ id: 1, _id: 0 })
    .sort({ id: 1 })
    .lean();

  let profileIds = profiles.map(p => p.id);  

  let totalPages = 0
  const totalProfiles = await Profile.countDocuments({ subcategories: subcategoryId })
  if(totalProfiles){
    totalPages = Math.ceil(totalProfiles / pageSize);
  }

  let bulkOps = [];

  for (let pageNo = 1; pageNo <= totalPages; pageNo++) {
    const startIndex = (pageNo - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const pageProfileIds = profileIds.slice(startIndex, endIndex);

    bulkOps.push({
      updateOne: {
        filter: { 
          subcategoryId: subcategoryId,
          pageNo: pageNo 
        },
        update: {
          $set: { 
            subcategoryId: subcategoryId,
            pageNo: pageNo,
            startId: pageProfileIds[0],
            totalPages: totalPages,
            updatedAt: new Date() 
          } 
        },
        upsert: true
      }
    });

    if (bulkOps.length === BATCH_SIZE || pageNo === totalPages) {
      await SitemapPaginatedSubcategories.bulkWrite(bulkOps);
      bulkOps = [];
    }
  }

  await SitemapPaginatedSubcategories.deleteMany({ subcategoryId, pageNo: { $gt: totalPages } });
}

async function createSitemapPaginationForSubcategory(startId = 0, batchSize = 100) {
  while(true) {
    const subcategories = await Subcategory
    .find({ id: { $gt: startId } })
    .sort({ id: 1 })
    .select({ id: 1, _id: 0 })
    .limit(batchSize)
    .lean();
    if(!subcategories.length) break;
    const subcategoryIds = subcategories.map(s => s.id);
    await Promise.all(
      subcategoryIds.map((subcategoryId) => createPaginatedProfilesForSubcategory(subcategoryId))
    );
    console.log(`Processed subcategories completed:`, subcategoryIds);
    startId = subcategoryIds[subcategoryIds.length - 1]
  }
}

async function createPaginatedSubcategoriesForCategory(categoryId) {
  
  const pageSize = getSitemapPaginationPageSize();

  let totalPages = 0
  const totalProfiles = await Subcategory.countDocuments({ category: categoryId })
  if(totalProfiles){
    totalPages = Math.ceil(totalProfiles / pageSize);
  }

  const subcategories = await Subcategory
    .find({ category: categoryId })
    .select({ id: 1, _id: 0 })
    .sort({ id: 1 })
    .lean();

  let subcategoryIds = subcategories.map(p => p.id);  

  let bulkOps = [];

  for (let pageNo = 1; pageNo <= totalPages; pageNo++) {
    const startIndex = (pageNo - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const pageSubcategoryIds = subcategoryIds.slice(startIndex, endIndex);

    bulkOps.push({
      updateOne: {
        filter: { 
          categoryId: categoryId, 
          pageNo: pageNo 
        },
        update: { 
          $set: { 
            categoryId: categoryId,
            pageNo: pageNo,
            startId: pageSubcategoryIds[0],
            totalPages: totalPages,
            updatedAt: new Date() 
          }
        },
        upsert: true
      }
    });

    if (bulkOps.length === BATCH_SIZE || pageNo === totalPages) {
      await SitemapPaginatedCategories.bulkWrite(bulkOps);
      bulkOps = [];
    }
  }

  await SitemapPaginatedCategories.deleteMany({ categoryId, pageNo: { $gt: totalPages } });

}

async function createSitemapPaginationForCategory(startId = 0, batchSize = 100) {
  while(true) {
    const categories = await Category.find({ id: { $gt: startId } })
    .sort({ id: 1 })
    .select({ id: 1, _id: 0 })
    .limit(batchSize)
    .lean();

    if(!categories.length) break;

    const categoryIds = categories.map(s => s.id);

    await Promise.all(
      categoryIds.map((categoryId) => createPaginatedSubcategoriesForCategory(categoryId))
    );
    console.log(`Processed categories completed:`, categoryIds);
    startId = categoryIds[categoryIds.length - 1]
  }
}

async function createSitemapPaginatedForProfiles() {
  const pageSize = getSitemapPaginationPageSize('profiles');
  const profiles = await Profile
    .find({})
    .select({ id: 1, _id: 0 })
    .sort({ id: 1 })
    .lean();

  let profileIds = profiles.map(p => p.id);

  let totalPages = 0
  const totalProfiles = await Profile.countDocuments({})
  if(totalProfiles){
    totalPages = Math.ceil(totalProfiles / pageSize);
  }

  let bulkOps = [];

  for (let pageNo = 1; pageNo <= totalPages; pageNo++) {
    const startIndex = (pageNo - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const pageProfileIds = profileIds.slice(startIndex, endIndex);

    bulkOps.push({
      updateOne: {
        filter: { 
          pageNo: pageNo 
        },
        update: { 
          $set: { 
            pageNo: pageNo,
            startId: pageProfileIds[0],
            totalPages: totalPages,
            updatedAt: new Date() 
          }
        },
        upsert: true
      }
    });

    if (bulkOps.length === BATCH_SIZE || pageNo === totalPages) {
      await SitemapPaginatedProfiles.bulkWrite(bulkOps);
      bulkOps = [];
    }
  }

  await SitemapPaginatedProfiles.deleteMany({ pageNo: { $gt: totalPages } });

}

module.exports = {
  createSitemapPaginationForSubcategory,
  createSitemapPaginationForCategory,
  createSitemapPaginatedForProfiles,
};
